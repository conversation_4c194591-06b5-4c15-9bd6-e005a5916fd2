const Order = require("../models/Order");
const { clearCache } = require('../config/redis');

exports.createOrder = async (req, res) => {
  try {
    const { items, total, status, shippingAddress, paymentMethod, notes, metadata } = req.body;

    // Get user ID from authenticated user (set by auth middleware)
    if (!req.user || !req.user._id) {
      return res.status(401).json({
        status: "fail",
        message: "User authentication required",
      });
    }

    if (!Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        status: "fail",
        message: "Order must have at least one item",
      });
    }

    // Validate that each item has required fields
    for (const item of items) {
      if (!item.productId || !item.name || !item.price || !item.quantity) {
        return res.status(400).json({
          status: "fail",
          message: "Each item must have productId, name, price, and quantity",
        });
      }
    }

    // Create order data with authenticated user ID
    const orderData = {
      user: req.user._id,
      items,
      total,
      status: status || "pending",
      shippingAddress,
      paymentMethod: paymentMethod || "cash_on_delivery",
      notes: notes || "",
      metadata: metadata || {},
    };

    const order = await Order.create(orderData);
    console.log("Created order:", order);

    // Invalidate orders cache
    await clearCache('api:/api/orders*');

    res.status(201).json({
      status: "success",
      data: {
        order,
      },
    });
  } catch (err) {
    console.error("Order creation error:", err);
    res.status(400).json({
      status: "fail",
      message: err.message,
    });
  }
};

exports.getAllOrders = async (req, res) => {
  try {
    const { user, search, status, dateFilter, page = 1, limit = 10 } = req.query;

    // Start with base filter
    let filter = {};

    // Filter orders by user if user query parameter is provided
    if (user) {
      filter.user = user;
    }

    // Apply search filter if provided
    if (search) {
      // Search in order items for product name or in shipping address
      filter.$or = [
        { 'items.name': { $regex: search, $options: 'i' } },
        { 'shippingAddress.name': { $regex: search, $options: 'i' } },
        { 'shippingAddress.address': { $regex: search, $options: 'i' } },
        { 'shippingAddress.city': { $regex: search, $options: 'i' } },
        { 'metadata.customerName': { $regex: search, $options: 'i' } },
        { 'metadata.customerEmail': { $regex: search, $options: 'i' } },
        { 'metadata.customerPhone': { $regex: search, $options: 'i' } },
        { 'metadata.shippingAddress': { $regex: search, $options: 'i' } }
      ];
    }

    // Apply status filter if provided
    if (status) {
      filter.status = status;
    }

    // Apply date filter if provided
    if (dateFilter) {
      const now = new Date();
      let dateQuery = {};

      switch (dateFilter) {
        case 'today':
          // Start of today
          const startOfToday = new Date(now.setHours(0, 0, 0, 0));
          dateQuery = { createdAt: { $gte: startOfToday } };
          break;
        case 'yesterday':
          // Start of yesterday
          const startOfYesterday = new Date(now);
          startOfYesterday.setDate(startOfYesterday.getDate() - 1);
          startOfYesterday.setHours(0, 0, 0, 0);
          // End of yesterday
          const endOfYesterday = new Date(now);
          endOfYesterday.setDate(endOfYesterday.getDate() - 1);
          endOfYesterday.setHours(23, 59, 59, 999);
          dateQuery = {
            createdAt: {
              $gte: startOfYesterday,
              $lte: endOfYesterday
            }
          };
          break;
        case 'last7days':
          // 7 days ago
          const last7Days = new Date(now);
          last7Days.setDate(last7Days.getDate() - 7);
          dateQuery = { createdAt: { $gte: last7Days } };
          break;
        case 'last30days':
          // 30 days ago
          const last30Days = new Date(now);
          last30Days.setDate(last30Days.getDate() - 30);
          dateQuery = { createdAt: { $gte: last30Days } };
          break;
        default:
          // No date filter or invalid filter
          break;
      }

      // Add date query to main filter if it's not empty
      if (Object.keys(dateQuery).length > 0) {
        filter = { ...filter, ...dateQuery };
      }
    }

    // Log the filter for debugging
    console.log('Order filter:', JSON.stringify(filter, null, 2));

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const pageSize = parseInt(limit);

    // Execute query with pagination
    const orders = await Order.find(filter)
      .sort('-createdAt')
      .skip(skip)
      .limit(pageSize);

    // Get total count for pagination
    const total = await Order.countDocuments(filter);

    res.status(200).json({
      status: "success",
      results: orders.length,
      total,
      totalPages: Math.ceil(total / pageSize),
      currentPage: parseInt(page),
      data: {
        orders,
      },
    });
  } catch (err) {
    console.error('Get all orders error:', err);
    res.status(404).json({
      status: "fail",
      message: err.message,
    });
  }
};

exports.getOrder = async (req, res) => {
  try {
    const id = req.params.id;
    const order = await Order.findById(id);

    if (!order) {
      return res.status(404).json({
        status: "fail",
        message: "Order not found",
      });
    }

    res.status(200).json({
      status: "success",
      data: {
        order,
      },
    });
  } catch (err) {
    res.status(404).json({
      status: "fail",
      message: err.message || "Error fetching order",
    });
  }
};

exports.updateOrder = async (req, res) => {
  try {
    const id = req.params.id;
    const order = await Order.findByIdAndUpdate(id, req.body, {
      new: true,
      runValidators: true,
    });

    // Invalidate both the specific order cache and the all orders cache
    await Promise.all([
      clearCache(`api:/api/orders/${id}`),
      clearCache('api:/api/orders*')
    ]);

    res.status(200).json({
      status: "success",
      data: {
        order,
      },
    });
  } catch (err) {
    res.status(404).json({
      status: "fail",
      message: err,
    });
  }
};

exports.deleteOrder = async (req, res) => {
  try {
    const id = req.params.id;
    await Order.findByIdAndDelete(id);

    // Invalidate both the specific order cache and the all orders cache
    await Promise.all([
      clearCache(`api:/api/orders/${id}`),
      clearCache('api:/api/orders*')
    ]);

    res.status(204).json({
      status: "success",
      data: null,
    });
  } catch (err) {
    res.status(404).json({
      status: "fail",
      message: err,
    });
  }
};
