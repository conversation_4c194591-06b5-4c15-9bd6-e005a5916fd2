const config = {
  development: {
    DEPLOYED_URL: 'http://localhost:5002',
    CLIENT_URL: 'http://localhost:3000',
    FRONTEND_AUTH_CALLBACK: 'http://localhost:3000/auth-callback'
  },
  production: {
    DEPLOYED_URL: 'https://cwa-backend.vercel.app',
    CLIENT_URL: 'https://chinioti-wooden-art.vercel.app',
    FRONTEND_AUTH_CALLBACK: 'https://chinioti-wooden-art.vercel.app/auth-callback'
  }
};

const environment = process.env.NODE_ENV || 'development';

module.exports = {
  ...config[environment],
  environment,
  isProduction: environment === 'production',
  isDevelopment: environment === 'development'
};
