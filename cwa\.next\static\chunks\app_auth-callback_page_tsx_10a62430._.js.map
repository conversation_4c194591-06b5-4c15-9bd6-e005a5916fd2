{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/app/auth-callback/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useEffect, useState, Suspense } from \"react\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\nimport { motion } from \"framer-motion\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { clearGoogleAuthInProgress } from \"@/lib/auth/authUtils\";\r\n\r\n// Loading fallback component\r\nfunction AuthCallbackLoading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-b from-white to-gray-50\">\r\n      <motion.div\r\n        className=\"text-center bg-white p-8 rounded-lg shadow-md max-w-md w-full\"\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        <motion.div\r\n          animate={{\r\n            scale: [1, 1.1, 1],\r\n            rotate: [0, 0, 0],\r\n          }}\r\n          transition={{\r\n            duration: 2,\r\n            repeat: Infinity,\r\n            repeatType: \"loop\",\r\n          }}\r\n          className=\"mx-auto mb-6 w-16 h-16 rounded-full bg-accent/10 flex items-center justify-center\"\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width=\"32\"\r\n            height=\"32\"\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"2\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            className=\"text-accent animate-spin\"\r\n          >\r\n            <path d=\"M21 12a9 9 0 1 1-6.219-8.56\"></path>\r\n          </svg>\r\n        </motion.div>\r\n\r\n        <h1 className=\"text-2xl font-bold mb-4\">Loading Authentication...</h1>\r\n        <p className=\"text-gray-600\">\r\n          Please wait while we process your authentication.\r\n        </p>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function AuthCallback() {\r\n  return (\r\n    <Suspense fallback={<AuthCallbackLoading />}>\r\n      <AuthCallbackContent />\r\n    </Suspense>\r\n  );\r\n}\r\n\r\nfunction AuthCallbackContent() {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const [isProcessing, setIsProcessing] = useState(true);\r\n  const [authStatus, setAuthStatus] = useState<\"success\" | \"error\" | null>(\r\n    null\r\n  );\r\n  const { setAuthState } = useAuth();\r\n\r\n  // Run once on component mount to process authentication\r\n  useEffect(() => {\r\n    // Check if we've already processed this auth callback\r\n    const hasProcessed =\r\n      typeof window !== \"undefined\" &&\r\n      sessionStorage.getItem(\"authCallbackProcessed\");\r\n\r\n    // This flag helps prevent multiple executions\r\n    let isHandled = false;\r\n\r\n    const processAuth = async () => {\r\n      if (isHandled || hasProcessed) return;\r\n      isHandled = true;\r\n\r\n      // Set a flag in session storage to prevent reprocessing\r\n      if (typeof window !== \"undefined\") {\r\n        sessionStorage.setItem(\"authCallbackProcessed\", \"true\");\r\n      }\r\n\r\n      console.log(\"Starting Google auth callback processing\");\r\n      setIsProcessing(true); // Ensure we're in processing state\r\n\r\n      try {\r\n        // Clear the Google auth in progress flag\r\n        clearGoogleAuthInProgress();\r\n        console.log(\"Cleared Google auth in progress flag\");\r\n\r\n        // Log all search parameters for debugging\r\n        const params = Object.fromEntries(searchParams.entries());\r\n        console.log(\"Processing auth callback with params:\", params);\r\n\r\n        // Check for error first\r\n        const error = searchParams.get(\"error\");\r\n        const errorMessage = searchParams.get(\"errorMessage\");\r\n\r\n        if (error) {\r\n          setAuthStatus(\"error\");\r\n          toast.error(errorMessage || \"Authentication failed\");\r\n          setTimeout(() => {\r\n            router.push(\"/sign-in\");\r\n            setIsProcessing(false);\r\n          }, 1500);\r\n          return;\r\n        }\r\n\r\n        const token = searchParams.get(\"token\");\r\n        const userId = searchParams.get(\"userId\");\r\n        const name = searchParams.get(\"name\");\r\n        const email = searchParams.get(\"email\");\r\n        const avatar = searchParams.get(\"avatar\");\r\n        const phone = searchParams.get(\"phone\");\r\n\r\n        if (token && userId) {\r\n          // Store auth data\r\n          localStorage.setItem(\"token\", token);\r\n          localStorage.setItem(\"userId\", userId);\r\n          localStorage.setItem(\"name\", name || \"\");\r\n          localStorage.setItem(\"email\", email || \"\");\r\n          if (avatar) localStorage.setItem(\"avatar\", avatar);\r\n          if (phone) localStorage.setItem(\"phone\", phone);\r\n\r\n          // Update auth state directly\r\n          setAuthState({\r\n            user: {\r\n              id: userId,\r\n              name: name || \"User\",\r\n              email: email || \"\",\r\n              avatar: avatar || undefined,\r\n              phone: phone || undefined,\r\n            },\r\n            isAuthenticated: true,\r\n            isLoading: false,\r\n            error: null,\r\n          });\r\n\r\n          setAuthStatus(\"success\");\r\n          toast.success(\"Successfully signed in with Google!\");\r\n\r\n          // Redirect to home page or dashboard after a short delay\r\n          setTimeout(() => {\r\n            // Clear the session storage flag before redirecting\r\n            if (typeof window !== \"undefined\") {\r\n              sessionStorage.removeItem(\"authCallbackProcessed\");\r\n            }\r\n\r\n            // Use replace instead of push to avoid browser history issues\r\n            router.replace(\"/\");\r\n            setIsProcessing(false);\r\n          }, 1500);\r\n        } else {\r\n          setAuthStatus(\"error\");\r\n          toast.error(\"Authentication failed - missing required data\");\r\n          setTimeout(() => {\r\n            router.replace(\"/sign-in\");\r\n            setIsProcessing(false);\r\n          }, 1500);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Auth callback error:\", error);\r\n        setAuthStatus(\"error\");\r\n        toast.error(\"An error occurred during authentication\");\r\n        setTimeout(() => {\r\n          router.replace(\"/sign-in\");\r\n          setIsProcessing(false);\r\n        }, 1500);\r\n      }\r\n    };\r\n\r\n    // Process auth if we have search params, haven't started processing yet, and haven't processed before\r\n    if (searchParams.toString() && authStatus === null && !hasProcessed) {\r\n      console.log(\"Starting auth processing with status:\", authStatus);\r\n      processAuth();\r\n    } else {\r\n      console.log(\r\n        \"Skipping auth processing. Status:\",\r\n        authStatus,\r\n        \"Params:\",\r\n        searchParams.toString() ? \"present\" : \"absent\",\r\n        \"Already processed:\",\r\n        hasProcessed ? \"yes\" : \"no\"\r\n      );\r\n    }\r\n\r\n    // Cleanup function to prevent memory leaks\r\n    return () => {\r\n      isHandled = true;\r\n\r\n      // Clear the session storage flag when the component is unmounted\r\n      // This is important for cases where the user might try to authenticate again\r\n      if (typeof window !== \"undefined\") {\r\n        sessionStorage.removeItem(\"authCallbackProcessed\");\r\n      }\r\n    };\r\n  }, [router, searchParams, setAuthState]);\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-b from-white to-gray-50\">\r\n      <motion.div\r\n        className=\"text-center bg-white p-8 rounded-lg shadow-md max-w-md w-full\"\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        <motion.div\r\n          animate={{\r\n            scale: [1, 1.1, 1],\r\n            rotate: [0, 0, 0],\r\n          }}\r\n          transition={{\r\n            duration: 2,\r\n            repeat: Infinity,\r\n            repeatType: \"loop\",\r\n          }}\r\n          className=\"mx-auto mb-6 w-16 h-16 rounded-full bg-accent/10 flex items-center justify-center\"\r\n        >\r\n          {authStatus === \"success\" ? (\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              width=\"32\"\r\n              height=\"32\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"2\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              className=\"text-green-500\"\r\n            >\r\n              <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\r\n              <polyline points=\"22 4 12 14.01 9 11.01\"></polyline>\r\n            </svg>\r\n          ) : authStatus === \"error\" ? (\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              width=\"32\"\r\n              height=\"32\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"2\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              className=\"text-red-500\"\r\n            >\r\n              <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\r\n              <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\"></line>\r\n              <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\"></line>\r\n            </svg>\r\n          ) : (\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              width=\"32\"\r\n              height=\"32\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"2\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              className=\"text-accent animate-spin\"\r\n            >\r\n              <path d=\"M21 12a9 9 0 1 1-6.219-8.56\"></path>\r\n            </svg>\r\n          )}\r\n        </motion.div>\r\n\r\n        <h1 className=\"text-2xl font-bold mb-4\">\r\n          {authStatus === \"success\"\r\n            ? \"Authentication Successful!\"\r\n            : authStatus === \"error\"\r\n            ? \"Authentication Failed\"\r\n            : \"Processing Authentication...\"}\r\n        </h1>\r\n\r\n        <p className=\"text-gray-600\">\r\n          {authStatus === \"success\"\r\n            ? \"You will be redirected to the homepage shortly.\"\r\n            : authStatus === \"error\"\r\n            ? \"There was a problem with your authentication. Redirecting you back to sign in.\"\r\n            : \"Please wait while we complete your authentication.\"}\r\n        </p>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAQA,6BAA6B;AAC7B,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;;8BAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,QAAQ;4BAAC;4BAAG;4BAAG;yBAAE;oBACnB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,YAAY;oBACd;oBACA,WAAU;8BAEV,cAAA,6LAAC;wBACC,OAAM;wBACN,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,WAAU;kCAEV,cAAA,6LAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;8BAIZ,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAMrC;KA5CS;AA8CM,SAAS;IACtB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBAAU,6LAAC;;;;;kBACnB,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB;AAQxB,SAAS;;IACP,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACzC;IAEF,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAE/B,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,sDAAsD;YACtD,MAAM,eACJ,aAAkB,eAClB,eAAe,OAAO,CAAC;YAEzB,8CAA8C;YAC9C,IAAI,YAAY;YAEhB,MAAM;6DAAc;oBAClB,IAAI,aAAa,cAAc;oBAC/B,YAAY;oBAEZ,wDAAwD;oBACxD,wCAAmC;wBACjC,eAAe,OAAO,CAAC,yBAAyB;oBAClD;oBAEA,QAAQ,GAAG,CAAC;oBACZ,gBAAgB,OAAO,mCAAmC;oBAE1D,IAAI;wBACF,yCAAyC;wBACzC,CAAA,GAAA,2HAAA,CAAA,4BAAyB,AAAD;wBACxB,QAAQ,GAAG,CAAC;wBAEZ,0CAA0C;wBAC1C,MAAM,SAAS,OAAO,WAAW,CAAC,aAAa,OAAO;wBACtD,QAAQ,GAAG,CAAC,yCAAyC;wBAErD,wBAAwB;wBACxB,MAAM,QAAQ,aAAa,GAAG,CAAC;wBAC/B,MAAM,eAAe,aAAa,GAAG,CAAC;wBAEtC,IAAI,OAAO;4BACT,cAAc;4BACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;4BAC5B;6EAAW;oCACT,OAAO,IAAI,CAAC;oCACZ,gBAAgB;gCAClB;4EAAG;4BACH;wBACF;wBAEA,MAAM,QAAQ,aAAa,GAAG,CAAC;wBAC/B,MAAM,SAAS,aAAa,GAAG,CAAC;wBAChC,MAAM,OAAO,aAAa,GAAG,CAAC;wBAC9B,MAAM,QAAQ,aAAa,GAAG,CAAC;wBAC/B,MAAM,SAAS,aAAa,GAAG,CAAC;wBAChC,MAAM,QAAQ,aAAa,GAAG,CAAC;wBAE/B,IAAI,SAAS,QAAQ;4BACnB,kBAAkB;4BAClB,aAAa,OAAO,CAAC,SAAS;4BAC9B,aAAa,OAAO,CAAC,UAAU;4BAC/B,aAAa,OAAO,CAAC,QAAQ,QAAQ;4BACrC,aAAa,OAAO,CAAC,SAAS,SAAS;4BACvC,IAAI,QAAQ,aAAa,OAAO,CAAC,UAAU;4BAC3C,IAAI,OAAO,aAAa,OAAO,CAAC,SAAS;4BAEzC,6BAA6B;4BAC7B,aAAa;gCACX,MAAM;oCACJ,IAAI;oCACJ,MAAM,QAAQ;oCACd,OAAO,SAAS;oCAChB,QAAQ,UAAU;oCAClB,OAAO,SAAS;gCAClB;gCACA,iBAAiB;gCACjB,WAAW;gCACX,OAAO;4BACT;4BAEA,cAAc;4BACd,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4BAEd,yDAAyD;4BACzD;6EAAW;oCACT,oDAAoD;oCACpD,wCAAmC;wCACjC,eAAe,UAAU,CAAC;oCAC5B;oCAEA,8DAA8D;oCAC9D,OAAO,OAAO,CAAC;oCACf,gBAAgB;gCAClB;4EAAG;wBACL,OAAO;4BACL,cAAc;4BACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4BACZ;6EAAW;oCACT,OAAO,OAAO,CAAC;oCACf,gBAAgB;gCAClB;4EAAG;wBACL;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,cAAc;wBACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ;yEAAW;gCACT,OAAO,OAAO,CAAC;gCACf,gBAAgB;4BAClB;wEAAG;oBACL;gBACF;;YAEA,sGAAsG;YACtG,IAAI,aAAa,QAAQ,MAAM,eAAe,QAAQ,CAAC,cAAc;gBACnE,QAAQ,GAAG,CAAC,yCAAyC;gBACrD;YACF,OAAO;gBACL,QAAQ,GAAG,CACT,qCACA,YACA,WACA,aAAa,QAAQ,KAAK,YAAY,UACtC,sBACA,eAAe,QAAQ;YAE3B;YAEA,2CAA2C;YAC3C;iDAAO;oBACL,YAAY;oBAEZ,iEAAiE;oBACjE,6EAA6E;oBAC7E,wCAAmC;wBACjC,eAAe,UAAU,CAAC;oBAC5B;gBACF;;QACF;wCAAG;QAAC;QAAQ;QAAc;KAAa;IAEvC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;;8BAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,QAAQ;4BAAC;4BAAG;4BAAG;yBAAE;oBACnB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,YAAY;oBACd;oBACA,WAAU;8BAET,eAAe,0BACd,6LAAC;wBACC,OAAM;wBACN,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,WAAU;;0CAEV,6LAAC;gCAAK,GAAE;;;;;;0CACR,6LAAC;gCAAS,QAAO;;;;;;;;;;;+BAEjB,eAAe,wBACjB,6LAAC;wBACC,OAAM;wBACN,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,WAAU;;0CAEV,6LAAC;gCAAO,IAAG;gCAAK,IAAG;gCAAK,GAAE;;;;;;0CAC1B,6LAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;;;;;;0CAC/B,6LAAC;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;gCAAK,IAAG;;;;;;;;;;;6CAGjC,6LAAC;wBACC,OAAM;wBACN,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,WAAU;kCAEV,cAAA,6LAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;8BAKd,6LAAC;oBAAG,WAAU;8BACX,eAAe,YACZ,+BACA,eAAe,UACf,0BACA;;;;;;8BAGN,6LAAC;oBAAE,WAAU;8BACV,eAAe,YACZ,oDACA,eAAe,UACf,mFACA;;;;;;;;;;;;;;;;;AAKd;GAzOS;;QACQ,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QAKX,2HAAA,CAAA,UAAO;;;MAPzB", "debugId": null}}]}