const userController = require('../controllers/userController');
const express = require('express');
const router = express.Router();
const { cacheMiddleware } = require('../config/redis');
const { protectAdmin } = require('../middleware/authMiddleware');

// Public routes
router.route('/').get(cacheMiddleware(300), userController.getUsers);
router.route('/:id').get(cacheMiddleware(300), userController.getUser);

// Admin-only routes
router.route('/:id').put(protectAdmin, userController.updateUser);
router.route('/:id').delete(protectAdmin, userController.deleteUser);

module.exports = router;