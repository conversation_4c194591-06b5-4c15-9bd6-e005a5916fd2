{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/app/auth-callback/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useEffect, useState, Suspense } from \"react\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\nimport { motion } from \"framer-motion\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { clearGoogleAuthInProgress } from \"@/lib/auth/authUtils\";\r\n\r\n// Loading fallback component\r\nfunction AuthCallbackLoading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-b from-white to-gray-50\">\r\n      <motion.div\r\n        className=\"text-center bg-white p-8 rounded-lg shadow-md max-w-md w-full\"\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        <motion.div\r\n          animate={{\r\n            scale: [1, 1.1, 1],\r\n            rotate: [0, 0, 0],\r\n          }}\r\n          transition={{\r\n            duration: 2,\r\n            repeat: Infinity,\r\n            repeatType: \"loop\",\r\n          }}\r\n          className=\"mx-auto mb-6 w-16 h-16 rounded-full bg-accent/10 flex items-center justify-center\"\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width=\"32\"\r\n            height=\"32\"\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"2\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            className=\"text-accent animate-spin\"\r\n          >\r\n            <path d=\"M21 12a9 9 0 1 1-6.219-8.56\"></path>\r\n          </svg>\r\n        </motion.div>\r\n\r\n        <h1 className=\"text-2xl font-bold mb-4\">Loading Authentication...</h1>\r\n        <p className=\"text-gray-600\">\r\n          Please wait while we process your authentication.\r\n        </p>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function AuthCallback() {\r\n  return (\r\n    <Suspense fallback={<AuthCallbackLoading />}>\r\n      <AuthCallbackContent />\r\n    </Suspense>\r\n  );\r\n}\r\n\r\nfunction AuthCallbackContent() {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const [isProcessing, setIsProcessing] = useState(true);\r\n  const [authStatus, setAuthStatus] = useState<\"success\" | \"error\" | null>(\r\n    null\r\n  );\r\n  const { setAuthState } = useAuth();\r\n\r\n  // Run once on component mount to process authentication\r\n  useEffect(() => {\r\n    // Check if we've already processed this auth callback\r\n    const hasProcessed =\r\n      typeof window !== \"undefined\" &&\r\n      sessionStorage.getItem(\"authCallbackProcessed\");\r\n\r\n    // This flag helps prevent multiple executions\r\n    let isHandled = false;\r\n\r\n    const processAuth = async () => {\r\n      if (isHandled || hasProcessed) return;\r\n      isHandled = true;\r\n\r\n      // Set a flag in session storage to prevent reprocessing\r\n      if (typeof window !== \"undefined\") {\r\n        sessionStorage.setItem(\"authCallbackProcessed\", \"true\");\r\n      }\r\n\r\n      console.log(\"Starting Google auth callback processing\");\r\n      setIsProcessing(true); // Ensure we're in processing state\r\n\r\n      try {\r\n        // Clear the Google auth in progress flag\r\n        clearGoogleAuthInProgress();\r\n        console.log(\"Cleared Google auth in progress flag\");\r\n\r\n        // Log all search parameters for debugging\r\n        const params = Object.fromEntries(searchParams.entries());\r\n        console.log(\"Processing auth callback with params:\", params);\r\n\r\n        // Check for error first\r\n        const error = searchParams.get(\"error\");\r\n        const errorMessage = searchParams.get(\"errorMessage\");\r\n\r\n        if (error) {\r\n          setAuthStatus(\"error\");\r\n          toast.error(errorMessage || \"Authentication failed\");\r\n          setTimeout(() => {\r\n            router.push(\"/sign-in\");\r\n            setIsProcessing(false);\r\n          }, 1500);\r\n          return;\r\n        }\r\n\r\n        const token = searchParams.get(\"token\");\r\n        const userId = searchParams.get(\"userId\");\r\n        const name = searchParams.get(\"name\");\r\n        const email = searchParams.get(\"email\");\r\n        const avatar = searchParams.get(\"avatar\");\r\n        const phone = searchParams.get(\"phone\");\r\n\r\n        if (token && userId) {\r\n          // Store auth data\r\n          localStorage.setItem(\"token\", token);\r\n          localStorage.setItem(\"userId\", userId);\r\n          localStorage.setItem(\"name\", name || \"\");\r\n          localStorage.setItem(\"email\", email || \"\");\r\n          if (avatar) localStorage.setItem(\"avatar\", avatar);\r\n          if (phone) localStorage.setItem(\"phone\", phone);\r\n\r\n          // Update auth state directly\r\n          setAuthState({\r\n            user: {\r\n              id: userId,\r\n              name: name || \"User\",\r\n              email: email || \"\",\r\n              avatar: avatar || undefined,\r\n              phone: phone || undefined,\r\n            },\r\n            isAuthenticated: true,\r\n            isLoading: false,\r\n            error: null,\r\n          });\r\n\r\n          setAuthStatus(\"success\");\r\n          toast.success(\"Successfully signed in with Google!\");\r\n\r\n          // Redirect to home page or dashboard after a short delay\r\n          setTimeout(() => {\r\n            // Clear the session storage flag before redirecting\r\n            if (typeof window !== \"undefined\") {\r\n              sessionStorage.removeItem(\"authCallbackProcessed\");\r\n            }\r\n\r\n            // Use replace instead of push to avoid browser history issues\r\n            router.replace(\"/\");\r\n            setIsProcessing(false);\r\n          }, 1500);\r\n        } else {\r\n          setAuthStatus(\"error\");\r\n          toast.error(\"Authentication failed - missing required data\");\r\n          setTimeout(() => {\r\n            router.replace(\"/sign-in\");\r\n            setIsProcessing(false);\r\n          }, 1500);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Auth callback error:\", error);\r\n        setAuthStatus(\"error\");\r\n        toast.error(\"An error occurred during authentication\");\r\n        setTimeout(() => {\r\n          router.replace(\"/sign-in\");\r\n          setIsProcessing(false);\r\n        }, 1500);\r\n      }\r\n    };\r\n\r\n    // Process auth if we have search params, haven't started processing yet, and haven't processed before\r\n    if (searchParams.toString() && authStatus === null && !hasProcessed) {\r\n      console.log(\"Starting auth processing with status:\", authStatus);\r\n      processAuth();\r\n    } else {\r\n      console.log(\r\n        \"Skipping auth processing. Status:\",\r\n        authStatus,\r\n        \"Params:\",\r\n        searchParams.toString() ? \"present\" : \"absent\",\r\n        \"Already processed:\",\r\n        hasProcessed ? \"yes\" : \"no\"\r\n      );\r\n    }\r\n\r\n    // Cleanup function to prevent memory leaks\r\n    return () => {\r\n      isHandled = true;\r\n\r\n      // Clear the session storage flag when the component is unmounted\r\n      // This is important for cases where the user might try to authenticate again\r\n      if (typeof window !== \"undefined\") {\r\n        sessionStorage.removeItem(\"authCallbackProcessed\");\r\n      }\r\n    };\r\n  }, [router, searchParams, setAuthState]);\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-b from-white to-gray-50\">\r\n      <motion.div\r\n        className=\"text-center bg-white p-8 rounded-lg shadow-md max-w-md w-full\"\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        <motion.div\r\n          animate={{\r\n            scale: [1, 1.1, 1],\r\n            rotate: [0, 0, 0],\r\n          }}\r\n          transition={{\r\n            duration: 2,\r\n            repeat: Infinity,\r\n            repeatType: \"loop\",\r\n          }}\r\n          className=\"mx-auto mb-6 w-16 h-16 rounded-full bg-accent/10 flex items-center justify-center\"\r\n        >\r\n          {authStatus === \"success\" ? (\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              width=\"32\"\r\n              height=\"32\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"2\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              className=\"text-green-500\"\r\n            >\r\n              <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\r\n              <polyline points=\"22 4 12 14.01 9 11.01\"></polyline>\r\n            </svg>\r\n          ) : authStatus === \"error\" ? (\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              width=\"32\"\r\n              height=\"32\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"2\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              className=\"text-red-500\"\r\n            >\r\n              <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\r\n              <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\"></line>\r\n              <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\"></line>\r\n            </svg>\r\n          ) : (\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              width=\"32\"\r\n              height=\"32\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"2\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              className=\"text-accent animate-spin\"\r\n            >\r\n              <path d=\"M21 12a9 9 0 1 1-6.219-8.56\"></path>\r\n            </svg>\r\n          )}\r\n        </motion.div>\r\n\r\n        <h1 className=\"text-2xl font-bold mb-4\">\r\n          {authStatus === \"success\"\r\n            ? \"Authentication Successful!\"\r\n            : authStatus === \"error\"\r\n            ? \"Authentication Failed\"\r\n            : \"Processing Authentication...\"}\r\n        </h1>\r\n\r\n        <p className=\"text-gray-600\">\r\n          {authStatus === \"success\"\r\n            ? \"You will be redirected to the homepage shortly.\"\r\n            : authStatus === \"error\"\r\n            ? \"There was a problem with your authentication. Redirecting you back to sign in.\"\r\n            : \"Please wait while we complete your authentication.\"}\r\n        </p>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAQA,6BAA6B;AAC7B,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;;8BAE5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,QAAQ;4BAAC;4BAAG;4BAAG;yBAAE;oBACnB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,YAAY;oBACd;oBACA,WAAU;8BAEV,cAAA,8OAAC;wBACC,OAAM;wBACN,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,WAAU;kCAEV,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;8BAIZ,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAMrC;AAEe,SAAS;IACtB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;;;;;kBACnB,cAAA,8OAAC;;;;;;;;;;AAGP;AAEA,SAAS;IACP,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACzC;IAEF,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAE/B,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sDAAsD;QACtD,MAAM,eACJ,gBAAkB,eAClB,eAAe,OAAO,CAAC;QAEzB,8CAA8C;QAC9C,IAAI,YAAY;QAEhB,MAAM,cAAc;YAClB,IAAI,aAAa,cAAc;YAC/B,YAAY;YAEZ,wDAAwD;YACxD,uCAAmC;;YAEnC;YAEA,QAAQ,GAAG,CAAC;YACZ,gBAAgB,OAAO,mCAAmC;YAE1D,IAAI;gBACF,yCAAyC;gBACzC,CAAA,GAAA,wHAAA,CAAA,4BAAyB,AAAD;gBACxB,QAAQ,GAAG,CAAC;gBAEZ,0CAA0C;gBAC1C,MAAM,SAAS,OAAO,WAAW,CAAC,aAAa,OAAO;gBACtD,QAAQ,GAAG,CAAC,yCAAyC;gBAErD,wBAAwB;gBACxB,MAAM,QAAQ,aAAa,GAAG,CAAC;gBAC/B,MAAM,eAAe,aAAa,GAAG,CAAC;gBAEtC,IAAI,OAAO;oBACT,cAAc;oBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;oBAC5B,WAAW;wBACT,OAAO,IAAI,CAAC;wBACZ,gBAAgB;oBAClB,GAAG;oBACH;gBACF;gBAEA,MAAM,QAAQ,aAAa,GAAG,CAAC;gBAC/B,MAAM,SAAS,aAAa,GAAG,CAAC;gBAChC,MAAM,OAAO,aAAa,GAAG,CAAC;gBAC9B,MAAM,QAAQ,aAAa,GAAG,CAAC;gBAC/B,MAAM,SAAS,aAAa,GAAG,CAAC;gBAChC,MAAM,QAAQ,aAAa,GAAG,CAAC;gBAE/B,IAAI,SAAS,QAAQ;oBACnB,kBAAkB;oBAClB,aAAa,OAAO,CAAC,SAAS;oBAC9B,aAAa,OAAO,CAAC,UAAU;oBAC/B,aAAa,OAAO,CAAC,QAAQ,QAAQ;oBACrC,aAAa,OAAO,CAAC,SAAS,SAAS;oBACvC,IAAI,QAAQ,aAAa,OAAO,CAAC,UAAU;oBAC3C,IAAI,OAAO,aAAa,OAAO,CAAC,SAAS;oBAEzC,6BAA6B;oBAC7B,aAAa;wBACX,MAAM;4BACJ,IAAI;4BACJ,MAAM,QAAQ;4BACd,OAAO,SAAS;4BAChB,QAAQ,UAAU;4BAClB,OAAO,SAAS;wBAClB;wBACA,iBAAiB;wBACjB,WAAW;wBACX,OAAO;oBACT;oBAEA,cAAc;oBACd,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAEd,yDAAyD;oBACzD,WAAW;wBACT,oDAAoD;wBACpD,uCAAmC;;wBAEnC;wBAEA,8DAA8D;wBAC9D,OAAO,OAAO,CAAC;wBACf,gBAAgB;oBAClB,GAAG;gBACL,OAAO;oBACL,cAAc;oBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,WAAW;wBACT,OAAO,OAAO,CAAC;wBACf,gBAAgB;oBAClB,GAAG;gBACL;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,cAAc;gBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,WAAW;oBACT,OAAO,OAAO,CAAC;oBACf,gBAAgB;gBAClB,GAAG;YACL;QACF;QAEA,sGAAsG;QACtG,IAAI,aAAa,QAAQ,MAAM,eAAe,QAAQ,CAAC,cAAc;YACnE,QAAQ,GAAG,CAAC,yCAAyC;YACrD;QACF,OAAO;YACL,QAAQ,GAAG,CACT,qCACA,YACA,WACA,aAAa,QAAQ,KAAK,YAAY,UACtC,sBACA,6EAAuB;QAE3B;QAEA,2CAA2C;QAC3C,OAAO;YACL,YAAY;YAEZ,iEAAiE;YACjE,6EAA6E;YAC7E,uCAAmC;;YAEnC;QACF;IACF,GAAG;QAAC;QAAQ;QAAc;KAAa;IAEvC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;;8BAE5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,QAAQ;4BAAC;4BAAG;4BAAG;yBAAE;oBACnB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,YAAY;oBACd;oBACA,WAAU;8BAET,eAAe,0BACd,8OAAC;wBACC,OAAM;wBACN,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,WAAU;;0CAEV,8OAAC;gCAAK,GAAE;;;;;;0CACR,8OAAC;gCAAS,QAAO;;;;;;;;;;;+BAEjB,eAAe,wBACjB,8OAAC;wBACC,OAAM;wBACN,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,WAAU;;0CAEV,8OAAC;gCAAO,IAAG;gCAAK,IAAG;gCAAK,GAAE;;;;;;0CAC1B,8OAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;;;;;;0CAC/B,8OAAC;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;gCAAK,IAAG;;;;;;;;;;;6CAGjC,8OAAC;wBACC,OAAM;wBACN,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,WAAU;kCAEV,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;8BAKd,8OAAC;oBAAG,WAAU;8BACX,eAAe,YACZ,+BACA,eAAe,UACf,0BACA;;;;;;8BAGN,8OAAC;oBAAE,WAAU;8BACV,eAAe,YACZ,oDACA,eAAe,UACf,mFACA;;;;;;;;;;;;;;;;;AAKd", "debugId": null}}]}