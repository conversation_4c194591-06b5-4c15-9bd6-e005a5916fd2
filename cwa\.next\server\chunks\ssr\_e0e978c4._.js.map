{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\nimport { CurrencyCode, CURRENCY_CONFIG } from '@/constants/helpers';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n// Legacy function - kept for backward compatibility\r\nexport function formatCurrency(amount: number): string {\r\n  return new Intl.NumberFormat('en-US', {\r\n    style: 'currency',\r\n    currency: 'PKR',\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 0,\r\n  }).format(amount);\r\n}\r\n\r\n// Enhanced currency formatting function\r\nexport function formatCurrencyWithCode(\r\n  amount: number,\r\n  currency: CurrencyCode = 'USD',\r\n  convertFromUSD: boolean = true\r\n): string {\r\n  const currencyInfo = CURRENCY_CONFIG[currency];\r\n\r\n  // Convert from USD to target currency if needed\r\n  const finalAmount = convertFromUSD\r\n    ? Math.round(amount * currencyInfo.rate)\r\n    : amount;\r\n\r\n  return new Intl.NumberFormat('en-US', {\r\n    style: 'currency',\r\n    currency: currency,\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 0,\r\n  }).format(finalAmount);\r\n}\r\n\r\n// Convert price between currencies\r\nexport function convertCurrency(\r\n  amount: number,\r\n  fromCurrency: CurrencyCode = 'USD',\r\n  toCurrency: CurrencyCode = 'USD'\r\n): number {\r\n  if (fromCurrency === toCurrency) return amount;\r\n\r\n  // Convert to USD first (base currency)\r\n  const amountInUSD = amount / CURRENCY_CONFIG[fromCurrency].rate;\r\n\r\n  // Convert from USD to target currency\r\n  const convertedAmount = amountInUSD * CURRENCY_CONFIG[toCurrency].rate;\r\n\r\n  return Math.round(convertedAmount);\r\n}\r\n\r\n// Format price with both currencies for comparison\r\nexport function formatDualCurrency(amount: number): string {\r\n  const usdAmount = formatCurrencyWithCode(amount, 'USD', false);\r\n  const pkrAmount = formatCurrencyWithCode(amount, 'PKR', true);\r\n  return `${usdAmount} / ${pkrAmount}`;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,uBACd,MAAc,EACd,WAAyB,KAAK,EAC9B,iBAA0B,IAAI;IAE9B,MAAM,eAAe,oHAAA,CAAA,kBAAe,CAAC,SAAS;IAE9C,gDAAgD;IAChD,MAAM,cAAc,iBAChB,KAAK,KAAK,CAAC,SAAS,aAAa,IAAI,IACrC;IAEJ,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,gBACd,MAAc,EACd,eAA6B,KAAK,EAClC,aAA2B,KAAK;IAEhC,IAAI,iBAAiB,YAAY,OAAO;IAExC,uCAAuC;IACvC,MAAM,cAAc,SAAS,oHAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,IAAI;IAE/D,sCAAsC;IACtC,MAAM,kBAAkB,cAAc,oHAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,IAAI;IAEtE,OAAO,KAAK,KAAK,CAAC;AACpB;AAGO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,YAAY,uBAAuB,QAAQ,OAAO;IACxD,MAAM,YAAY,uBAAuB,QAAQ,OAAO;IACxD,OAAO,GAAG,UAAU,GAAG,EAAE,WAAW;AACtC", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"uppercase inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive min-h-[44px] min-w-[44px]\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-accent text-background shadow-xs hover:bg-accent/90 cursor-pointer\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent/50 hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"underline-offset-4  hover:underline hover:text-accent cursor-pointer\",\r\n      },\r\n      size: {\r\n        default: \"h-11 py-2 px-4 m-1 has-[>svg]:px-3\",\r\n        sm: \"h-10 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-12 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-11\",\r\n      },\r\n      color: {\r\n        default: \"\",\r\n        light: \"text-forground\",\r\n        dark: \"text-background\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"link\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  color,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & VariantProps<typeof buttonVariants>) {\r\n  return (\r\n    <button\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className, color }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,meACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;QACA,OAAO;YACL,SAAS;YACT,OAAO;YACP,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,KAAK,EACL,GAAG,OACkE;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;YAAW;QAAM;QAC9D,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/auth/GoogleSignInButton.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport Image from \"next/image\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\n\r\ninterface GoogleSignInButtonProps {\r\n  text?: string;\r\n  mode?: \"signin\" | \"signup\";\r\n  className?: string;\r\n  onSuccess?: () => void;\r\n  onError?: (error: unknown) => void;\r\n}\r\n\r\nconst GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({\r\n  text = \"Continue with Google\",\r\n  mode = \"signin\",\r\n  className = \"\",\r\n  onSuccess,\r\n  onError,\r\n}) => {\r\n  const { loginWithGoogle, isLoading } = useAuth();\r\n\r\n  const handleGoogleSignIn = async () => {\r\n    try {\r\n      console.log(`Initiating Google ${mode} process`);\r\n      await loginWithGoogle();\r\n      console.log(\"Google login function called successfully\");\r\n\r\n      // Call onSuccess callback if provided\r\n      if (onSuccess) {\r\n        console.log(\"Calling onSuccess callback\");\r\n        onSuccess();\r\n      }\r\n    } catch (error) {\r\n      console.error(`Google ${mode} error:`, error);\r\n\r\n      // Call onError callback if provided\r\n      if (onError) {\r\n        console.log(\"Calling onError callback\");\r\n        onError(error);\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.button\r\n      onClick={handleGoogleSignIn}\r\n      disabled={isLoading}\r\n      className={`w-full flex items-center justify-center gap-2 px-4 py-2.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-accent/30 disabled:opacity-70 disabled:cursor-not-allowed transition-all duration-200 ${className}`}\r\n      whileHover={{ scale: 1.01 }}\r\n      whileTap={{ scale: 0.98 }}\r\n    >\r\n      {isLoading ? (\r\n        <div className=\"h-5 w-5 border-2 border-gray-300 border-t-accent rounded-full animate-spin\" />\r\n      ) : (\r\n        <Image\r\n          src=\"/assets/icons/google-logo.svg\"\r\n          alt=\"Google logo\"\r\n          width={20}\r\n          height={20}\r\n          className=\"mr-1\"\r\n        />\r\n      )}\r\n      <span>\r\n        {isLoading\r\n          ? `${mode === \"signin\" ? \"Signing in\" : \"Signing up\"}...`\r\n          : text}\r\n      </span>\r\n    </motion.button>\r\n  );\r\n};\r\n\r\nexport default GoogleSignInButton;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcA,MAAM,qBAAwD,CAAC,EAC7D,OAAO,sBAAsB,EAC7B,OAAO,QAAQ,EACf,YAAY,EAAE,EACd,SAAS,EACT,OAAO,EACR;IACC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAE7C,MAAM,qBAAqB;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,KAAK,QAAQ,CAAC;YAC/C,MAAM;YACN,QAAQ,GAAG,CAAC;YAEZ,sCAAsC;YACtC,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC;gBACZ;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC,EAAE;YAEvC,oCAAoC;YACpC,IAAI,SAAS;gBACX,QAAQ,GAAG,CAAC;gBACZ,QAAQ;YACV;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,UAAU;QACV,WAAW,CAAC,mSAAmS,EAAE,WAAW;QAC5T,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;;YAEvB,0BACC,8OAAC;gBAAI,WAAU;;;;;qCAEf,8OAAC,6HAAA,CAAA,UAAK;gBACJ,KAAI;gBACJ,KAAI;gBACJ,OAAO;gBACP,QAAQ;gBACR,WAAU;;;;;;0BAGd,8OAAC;0BACE,YACG,GAAG,SAAS,WAAW,eAAe,aAAa,GAAG,CAAC,GACvD;;;;;;;;;;;;AAIZ;uCAEe", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/auth/OrDivider.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\n\r\ninterface OrDividerProps {\r\n  text?: string;\r\n  className?: string;\r\n}\r\n\r\nconst OrDivider: React.FC<OrDividerProps> = ({\r\n  text = 'OR',\r\n  className = '',\r\n}) => {\r\n  return (\r\n    <div className={`relative my-6 ${className}`}>\r\n      <div className=\"absolute inset-0 flex items-center\">\r\n        <div className=\"w-full border-t border-gray-300\" />\r\n      </div>\r\n      <div className=\"relative flex justify-center text-sm\">\r\n        <motion.span \r\n          className=\"px-4 bg-card text-gray-500 uppercase\"\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.3, delay: 0.2 }}\r\n        >\r\n          {text}\r\n        </motion.span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OrDivider;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASA,MAAM,YAAsC,CAAC,EAC3C,OAAO,IAAI,EACX,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,cAAc,EAAE,WAAW;;0BAC1C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAEvC;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/auth/PublicRoute.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { ReactNode, useEffect, Suspense } from \"react\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\n\r\ninterface PublicRouteProps {\r\n  children: ReactNode;\r\n  redirectTo?: string;\r\n}\r\n\r\n// Loading component for PublicRoute\r\nconst PublicRouteLoading = () => {\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-[50vh]\">\r\n      <div className=\"text-center\">\r\n        <div className=\"h-12 w-12 border-4 border-accent border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\r\n        <p className=\"text-gray-500\">Loading authentication state...</p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst PublicRoute: React.FC<PublicRouteProps> = ({\r\n  children,\r\n  redirectTo = \"/\",\r\n}) => {\r\n  return (\r\n    <Suspense fallback={<PublicRouteLoading />}>\r\n      <PublicRouteContent children={children} redirectTo={redirectTo} />\r\n    </Suspense>\r\n  );\r\n};\r\n\r\nconst PublicRouteContent: React.FC<PublicRouteProps> = ({\r\n  children,\r\n  redirectTo = \"/\",\r\n}) => {\r\n  const { isAuthenticated, isLoading } = useAuth();\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const redirectPath = searchParams.get(\"redirect\");\r\n\r\n  useEffect(() => {\r\n    if (!isLoading && isAuthenticated) {\r\n      router.push(redirectPath || redirectTo);\r\n    }\r\n  }, [isAuthenticated, isLoading, router, redirectTo, redirectPath]);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-[50vh]\">\r\n        <div className=\"text-center\">\r\n          <div className=\"h-12 w-12 border-4 border-accent border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-500\">Loading authentication state...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isAuthenticated) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-[50vh]\">\r\n        <div className=\"text-center\">\r\n          <div className=\"h-12 w-12 text-green-500 mx-auto mb-4\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"2\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n            >\r\n              <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\r\n              <polyline points=\"22 4 12 14.01 9 11.01\"></polyline>\r\n            </svg>\r\n          </div>\r\n          <p className=\"text-gray-700\">You&apos;re already signed in!</p>\r\n          <p className=\"text-gray-500 text-sm mt-2\">Redirecting you...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return <>{children}</>;\r\n};\r\n\r\nexport default PublicRoute;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWA,oCAAoC;AACpC,MAAM,qBAAqB;IACzB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIrC;AAEA,MAAM,cAA0C,CAAC,EAC/C,QAAQ,EACR,aAAa,GAAG,EACjB;IACC,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;;;;;kBACnB,cAAA,8OAAC;YAAmB,UAAU;YAAU,YAAY;;;;;;;;;;;AAG1D;AAEA,MAAM,qBAAiD,CAAC,EACtD,QAAQ,EACR,aAAa,GAAG,EACjB;IACC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,eAAe,aAAa,GAAG,CAAC;IAEtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,iBAAiB;YACjC,OAAO,IAAI,CAAC,gBAAgB;QAC9B;IACF,GAAG;QAAC;QAAiB;QAAW;QAAQ;QAAY;KAAa;IAEjE,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,iBAAiB;QACnB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,OAAM;4BACN,SAAQ;4BACR,MAAK;4BACL,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,gBAAe;;8CAEf,8OAAC;oCAAK,GAAE;;;;;;8CACR,8OAAC;oCAAS,QAAO;;;;;;;;;;;;;;;;;kCAGrB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,qBAAO;kBAAG;;AACZ;uCAEe", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/app/%28auth%29/sign-up/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect, Suspense } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport Link from \"next/link\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { toast } from \"sonner\";\r\nimport GoogleSignInButton from \"@/components/auth/GoogleSignInButton\";\r\nimport OrDivider from \"@/components/auth/OrDivider\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport PublicRoute from \"@/components/auth/PublicRoute\";\r\n\r\n// Loading fallback component\r\nconst SignUpLoading = () => {\r\n  return (\r\n    <PublicRoute>\r\n      <div className=\"bg-card rounded-lg shadow-lg p-8 w-full\">\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ duration: 0.3 }}\r\n        >\r\n          <h2 className=\"text-2xl font-bold text-center mb-6\">\r\n            Create an Account\r\n          </h2>\r\n          <div className=\"flex justify-center items-center py-8\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              width=\"32\"\r\n              height=\"32\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"2\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              className=\"text-accent animate-spin\"\r\n            >\r\n              <path d=\"M21 12a9 9 0 1 1-6.219-8.56\"></path>\r\n            </svg>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </PublicRoute>\r\n  );\r\n};\r\n\r\nconst SignUp = () => {\r\n  return (\r\n    <Suspense fallback={<SignUpLoading />}>\r\n      <SignUpContent />\r\n    </Suspense>\r\n  );\r\n};\r\n\r\nconst SignUpContent = () => {\r\n  const { signup, loginWithGoogle, isLoading, error } = useAuth();\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const redirectPath = searchParams.get(\"redirect\");\r\n\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    password: \"\",\r\n    confirmPassword: \"\",\r\n    phone: \"\",\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (error) {\r\n      toast.error(error);\r\n    }\r\n  }, [error]);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [name]: value,\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Basic validation\r\n    if (\r\n      !formData.name ||\r\n      !formData.email ||\r\n      !formData.password ||\r\n      !formData.confirmPassword ||\r\n      !formData.phone\r\n    ) {\r\n      toast.error(\"Please fill in all fields\");\r\n      return;\r\n    }\r\n\r\n    if (formData.password !== formData.confirmPassword) {\r\n      toast.error(\"Passwords do not match\");\r\n      return;\r\n    }\r\n\r\n    // Validate phone number (11 digits)\r\n    if (!/^\\d{11}$/.test(formData.phone)) {\r\n      toast.error(\"Phone number must be exactly 11 digits\");\r\n      return;\r\n    }\r\n\r\n    await signup(\r\n      formData.name,\r\n      formData.email,\r\n      formData.password,\r\n      formData.phone\r\n    );\r\n  };\r\n\r\n  const handleGoogleSignIn = async () => {\r\n    await loginWithGoogle();\r\n  };\r\n\r\n  return (\r\n    <PublicRoute>\r\n      <div className=\"bg-card rounded-lg shadow-lg p-8 w-full\">\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ duration: 0.3 }}\r\n        >\r\n          <h2 className=\"text-2xl font-bold text-center mb-6\">\r\n            Create an Account\r\n          </h2>\r\n\r\n          {/* Google Sign-Up Button */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.1 }}\r\n          >\r\n            <GoogleSignInButton\r\n              text=\"Sign up with Google\"\r\n              mode=\"signup\"\r\n              onSuccess={handleGoogleSignIn}\r\n            />\r\n          </motion.div>\r\n\r\n          <OrDivider text=\"or sign up with email\" />\r\n\r\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n            <div className=\"space-y-2\">\r\n              <label htmlFor=\"name\" className=\"block text-sm font-medium\">\r\n                Full Name\r\n              </label>\r\n              <motion.div whileFocus={{ scale: 1.01 }} className=\"relative\">\r\n                <input\r\n                  id=\"name\"\r\n                  name=\"name\"\r\n                  type=\"text\"\r\n                  required\r\n                  value={formData.name}\r\n                  onChange={handleChange}\r\n                  className=\"w-full px-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all\"\r\n                  placeholder=\"John Doe\"\r\n                />\r\n              </motion.div>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <label htmlFor=\"email\" className=\"block text-sm font-medium\">\r\n                Email Address\r\n              </label>\r\n              <motion.div whileFocus={{ scale: 1.01 }} className=\"relative\">\r\n                <input\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  required\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  className=\"w-full px-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all\"\r\n                  placeholder=\"<EMAIL>\"\r\n                />\r\n              </motion.div>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <label htmlFor=\"password\" className=\"block text-sm font-medium\">\r\n                Password\r\n              </label>\r\n              <motion.div whileFocus={{ scale: 1.01 }} className=\"relative\">\r\n                <input\r\n                  id=\"password\"\r\n                  name=\"password\"\r\n                  type=\"password\"\r\n                  required\r\n                  value={formData.password}\r\n                  onChange={handleChange}\r\n                  className=\"w-full px-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all\"\r\n                  placeholder=\"••••••••\"\r\n                />\r\n              </motion.div>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <label\r\n                htmlFor=\"confirmPassword\"\r\n                className=\"block text-sm font-medium\"\r\n              >\r\n                Confirm Password\r\n              </label>\r\n              <motion.div whileFocus={{ scale: 1.01 }} className=\"relative\">\r\n                <input\r\n                  id=\"confirmPassword\"\r\n                  name=\"confirmPassword\"\r\n                  type=\"password\"\r\n                  required\r\n                  value={formData.confirmPassword}\r\n                  onChange={handleChange}\r\n                  className=\"w-full px-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all\"\r\n                  placeholder=\"••••••••\"\r\n                />\r\n              </motion.div>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <label htmlFor=\"phone\" className=\"block text-sm font-medium\">\r\n                Phone Number\r\n              </label>\r\n              <motion.div whileFocus={{ scale: 1.01 }} className=\"relative\">\r\n                <input\r\n                  id=\"phone\"\r\n                  name=\"phone\"\r\n                  type=\"tel\"\r\n                  required\r\n                  value={formData.phone}\r\n                  onChange={handleChange}\r\n                  className=\"w-full px-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all\"\r\n                  placeholder=\"03001234567\"\r\n                  pattern=\"[0-9]{11}\"\r\n                  title=\"Phone number must be exactly 11 digits\"\r\n                />\r\n              </motion.div>\r\n            </div>\r\n\r\n            <motion.div\r\n              whileHover={{ scale: 1.02 }}\r\n              whileTap={{ scale: 0.98 }}\r\n              className=\"pt-2\"\r\n            >\r\n              <Button\r\n                type=\"submit\"\r\n                variant=\"default\"\r\n                className=\"w-full py-2\"\r\n                disabled={isLoading}\r\n              >\r\n                {isLoading ? \"Creating Account...\" : \"Sign Up\"}\r\n              </Button>\r\n            </motion.div>\r\n          </form>\r\n\r\n          <div className=\"mt-6 text-center text-sm\">\r\n            <p>\r\n              Already have an account?{\" \"}\r\n              <Link\r\n                href=\"/sign-in\"\r\n                className=\"text-accent hover:underline font-medium\"\r\n              >\r\n                Sign In\r\n              </Link>\r\n            </p>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </PublicRoute>\r\n  );\r\n};\r\n\r\nexport default SignUp;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;;AAYA,6BAA6B;AAC7B,MAAM,gBAAgB;IACpB,qBACE,8OAAC,kIAAA,CAAA,UAAW;kBACV,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,OAAM;4BACN,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;4BACL,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,gBAAe;4BACf,WAAU;sCAEV,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;;;;;kBACnB,cAAA,8OAAC;;;;;;;;;;AAGP;AAEA,MAAM,gBAAgB;IACpB,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC5D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,eAAe,aAAa,GAAG,CAAC;IAEtC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,mBAAmB;QACnB,IACE,CAAC,SAAS,IAAI,IACd,CAAC,SAAS,KAAK,IACf,CAAC,SAAS,QAAQ,IAClB,CAAC,SAAS,eAAe,IACzB,CAAC,SAAS,KAAK,EACf;YACA,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,oCAAoC;QACpC,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,KAAK,GAAG;YACpC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,OACJ,SAAS,IAAI,EACb,SAAS,KAAK,EACd,SAAS,QAAQ,EACjB,SAAS,KAAK;IAElB;IAEA,MAAM,qBAAqB;QACzB,MAAM;IACR;IAEA,qBACE,8OAAC,kIAAA,CAAA,UAAW;kBACV,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAKpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,8OAAC,yIAAA,CAAA,UAAkB;4BACjB,MAAK;4BACL,MAAK;4BACL,WAAW;;;;;;;;;;;kCAIf,8OAAC,gIAAA,CAAA,UAAS;wBAAC,MAAK;;;;;;kCAEhB,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAO,WAAU;kDAA4B;;;;;;kDAG5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAAC,YAAY;4CAAE,OAAO;wCAAK;wCAAG,WAAU;kDACjD,cAAA,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,IAAI;4CACpB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAA4B;;;;;;kDAG7D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAAC,YAAY;4CAAE,OAAO;wCAAK;wCAAG,WAAU;kDACjD,cAAA,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAA4B;;;;;;kDAGhE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAAC,YAAY;4CAAE,OAAO;wCAAK;wCAAG,WAAU;kDACjD,cAAA,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAAC,YAAY;4CAAE,OAAO;wCAAK;wCAAG,WAAU;kDACjD,cAAA,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,eAAe;4CAC/B,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAA4B;;;;;;kDAG7D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAAC,YAAY;4CAAE,OAAO;wCAAK;wCAAG,WAAU;kDACjD,cAAA,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,SAAQ;4CACR,OAAM;;;;;;;;;;;;;;;;;0CAKZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CAEV,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,UAAU;8CAET,YAAY,wBAAwB;;;;;;;;;;;;;;;;;kCAK3C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;gCAAE;gCACwB;8CACzB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}]}